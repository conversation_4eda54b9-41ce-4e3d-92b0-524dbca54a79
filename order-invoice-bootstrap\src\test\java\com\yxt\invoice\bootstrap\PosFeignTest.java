package com.yxt.invoice.bootstrap;

import com.yxt.invoice.infrastructure.provider.dto.pos.PosInvoiceQueryData;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosInvoiceQueryReq;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosResponse;
import com.yxt.invoice.infrastructure.provider.feign.HdPosFeign;
import javax.annotation.Resource;
import org.junit.Test;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PosFeignTest extends BaseTest {

  @Resource
  private HdPosFeign hdPosFeign;


  @Test
  public void test1() {
    PosInvoiceQueryReq req = new PosInvoiceQueryReq();
    req.setThirdOrderNo("1125081600011140");
    req.setStoreCode("F005");

    try{
      log.info("开始调用POS发票查询接口，参数：{}", req);
      PosResponse<PosInvoiceQueryData> response = hdPosFeign.invoiceQuery(req);
      log.info("POS发票查询接口响应：{}", response);

      if (response.success()) {
        log.info("调用成功，返回数据：{}", response.getData());
      } else {
        log.warn("调用失败，状态：{}，消息：{}", response.getStatus(), response.getMsg());
      }
    } catch (Exception e) {
      log.error("调用POS发票查询接口异常", e);
    }
  }
}
