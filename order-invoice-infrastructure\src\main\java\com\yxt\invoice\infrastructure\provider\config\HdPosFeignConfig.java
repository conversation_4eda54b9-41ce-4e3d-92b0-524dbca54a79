package com.yxt.invoice.infrastructure.provider.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 海典POS Feign配置
 *
 * @author: moatkon
 * @time: 2024/12/12 14:43
 */
@Configuration
@Slf4j
public class HdPosFeignConfig {

    /**
     * 请求拦截器，确保Content-Type正确设置
     */
    @Bean
    public RequestInterceptor hdPosRequestInterceptor() {
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate template) {
                // 确保Content-Type为application/json
                template.header("Content-Type", "application/json");
                
                // 记录请求信息用于调试
                log.debug("HdPos Feign Request - URL: {}, Method: {}, Headers: {}", 
                    template.url(), template.method(), template.headers());
                
                // 记录请求体
                if (template.body() != null) {
                    log.debug("HdPos Feign Request Body: {}", new String(template.body()));
                }
            }
        };
    }
}
