package com.yxt.invoice.infrastructure.provider.feign;


import com.yxt.invoice.infrastructure.provider.dto.pos.PosInvoiceQueryReq;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosInvoiceQueryData;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosResponse;
import com.yxt.invoice.infrastructure.provider.config.HdPosFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 海典Pos接口
 *
 * @author: moatkon
 * @time: 2024/12/12 14:43
 */
@FeignClient(name = "hd-pos-service", url = "${remote.pos.url}", configuration = HdPosFeignConfig.class)
public interface HdPosFeign {


    @PostMapping("/api/pos/invoice/query")
    PosResponse<List<PosInvoiceQueryData>> invoiceQuery(@RequestBody PosInvoiceQueryReq req);


}
